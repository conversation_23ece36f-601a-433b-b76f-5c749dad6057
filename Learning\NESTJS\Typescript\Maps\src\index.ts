/// <reference types="@types/google.maps" />

import { CustomMap } from "./CustomMap";
import { User } from "./User";
// import { Company } from "./Company.ts";

const user = new User();
// const company = new Company();

// console.log(user);
// console.log(company);

// Function to initialize the map
function initMap(): void {
    const mapElement = document.getElementById('map') as HTMLElement;

    if (mapElement) {
        console.log('Creating user:', user);
        console.log('User location:', user.location);

        const customMap = new CustomMap('map');
        customMap.addUserMarker(user);
        console.log('Map initialized successfully!');
    } else {
        console.error('Map element not found!');
    }
}

// Wait for the Google Maps API to load, then initialize the map
function waitForGoogleMaps(): void {
    if (typeof google !== 'undefined' && google.maps) {
        // Google Maps API is loaded, initialize the map
        initMap();
    } else {
        // Google Maps API not loaded yet, wait a bit and try again
        setTimeout(waitForGoogleMaps, 100);
    }
}

// Start checking for Google Maps API when the DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', waitForGoogleMaps);
} else {
    // DOM is already ready
    waitForGoogleMaps();
}

