import { User } from "./User";
import { Company } from "./Company";

export class CustomMap {
    private googleMap: google.maps.Map;

    constructor(divId: string) {
        this.googleMap = new google.maps.Map(document.getElementById(divId) as HTMLElement, {
            zoom: 4,
            center: {
                lat: 0,
                lng: 0
            }
        });
    }

    addUserMarker(user: User): void {
        console.log('Adding user marker at:', user.location);
        console.log('User name:', user.name);

        new google.maps.Marker({
            map: this.googleMap,
            position: {
                lat: user.location.lat,
                lng: user.location.lng
            },
            title: `User: ${user.name}`,
            animation: google.maps.Animation.DROP
        });

        // Center the map on the user's location
        this.googleMap.setCenter(user.location);
        this.googleMap.setZoom(10);

        console.log('Marker created successfully');
    }

    addCompanyMarker(company: Company): void {}
}