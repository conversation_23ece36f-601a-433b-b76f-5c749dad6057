import { User } from "./User";
import { Company } from "./Company";

export class CustomMap {
    private googleMap: google.maps.Map;

    constructor(divId: string) {
        console.log('Creating CustomMap...');
        this.googleMap = new google.maps.Map(document.getElementById(divId) as HTMLElement, {
            zoom: 10,
            center: {
                lat: 40.7128, // New York City
                lng: -74.0060
            }
        });
        console.log('Google Map created');

        // Add a test marker at NYC to see if markers work at all
        this.addTestMarker();
    }

    private addTestMarker(): void {
        console.log('Adding test marker at NYC...');
        new google.maps.Marker({
            map: this.googleMap,
            position: {
                lat: 40.7128,
                lng: -74.0060
            },
            title: 'Test Marker - NYC',
            animation: google.maps.Animation.BOUNCE
        });
        console.log('Test marker added');
    }

    addUserMarker(user: User): void {
        console.log('Adding user marker at:', user.location);
        console.log('User name:', user.name);

        new google.maps.Marker({
            map: this.googleMap,
            position: {
                lat: user.location.lat,
                lng: user.location.lng
            },
            title: `User: ${user.name}`,
            animation: google.maps.Animation.DROP
        });

        // Center the map on the user's location
        this.googleMap.setCenter(user.location);
        this.googleMap.setZoom(10);

        console.log('Marker created successfully');
    }

    addCompanyMarker(company: Company): void {}
}